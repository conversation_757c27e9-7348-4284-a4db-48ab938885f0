import React from 'react';
import { theme } from '../../config/theme.config';

interface DashboardHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  title,
  description,
  actions,
  className = ''
}) => {
  return (
    <div
      className={className}
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        paddingBottom: theme.spacing.xl,
        borderBottom: `1px solid ${theme.colors.border.light}`,
        marginBottom: theme.spacing.xl,
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%)',
        borderRadius: theme.borderRadius.xl,
        padding: `${theme.spacing.xxl}px ${theme.spacing.xl}px`,
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        position: 'relative',
        overflow: 'hidden',
        flexDirection: 'row',
        gap: theme.spacing.lg,
        flexWrap: 'wrap',
        backdropFilter: 'blur(10px)',
        border: `1px solid rgba(255, 255, 255, 0.2)`,
      }}
    >
      {/* Background decorations */}
      <div style={{
        position: 'absolute',
        top: '-50px',
        right: '-50px',
        width: '300px',
        height: '300px',
        background: `radial-gradient(circle, ${theme.colors.primary}15 0%, transparent 70%)`,
        borderRadius: '50%',
        pointerEvents: 'none',
      }} />
      <div style={{
        position: 'absolute',
        bottom: '-30px',
        left: '-30px',
        width: '200px',
        height: '200px',
        background: `radial-gradient(circle, ${theme.colors.accent}10 0%, transparent 70%)`,
        borderRadius: '50%',
        pointerEvents: 'none',
      }} />

      <div style={{ flex: 1, zIndex: 1 }}>
        <h1 style={{
          margin: 0,
          color: theme.colors.text.primary,
          fontSize: '42px',
          fontWeight: 900,
          letterSpacing: '-1.2px',
          lineHeight: 1.1,
          background: `linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.primaryLight}, ${theme.colors.accent})`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          marginBottom: theme.spacing.md,
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        }}>
          {title}
        </h1>
        {description && (
          <p style={{
            margin: 0,
            color: theme.colors.text.secondary,
            fontSize: '20px',
            lineHeight: 1.6,
            maxWidth: '700px',
            fontWeight: 500,
            letterSpacing: '0.2px',
          }}>
            {description}
          </p>
        )}
      </div>
      
      {actions && (
        <div style={{
          display: 'flex',
          gap: theme.spacing.md,
          alignItems: 'center',
          flexShrink: 0,
          zIndex: 1,
        }}>
          {actions}
        </div>
      )}
    </div>
  );
};
