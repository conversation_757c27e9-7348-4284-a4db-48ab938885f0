import React from 'react';
import { theme } from '../../config/theme.config';

interface Tab {
  id: string;
  label: string;
  icon?: string;
  disabled?: boolean;
}

interface TabNavigationProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  variant?: 'default' | 'pills' | 'underline';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
  variant = 'pills',
  size = 'md',
  className = ''
}) => {
  const sizeStyles = {
    sm: {
      padding: `${theme.spacing.xs}px ${theme.spacing.sm}px`,
      fontSize: '12px',
      gap: '1px',
    },
    md: {
      padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
      fontSize: '14px',
      gap: '2px',
    },
    lg: {
      padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
      fontSize: '16px',
      gap: '3px',
    },
  };

  const containerStyle = {
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    gap: sizeStyles[size].gap,
    backgroundColor: variant === 'pills' ? 'rgba(255, 255, 255, 0.8)' : 'transparent',
    borderRadius: variant === 'pills' ? theme.borderRadius.xxl : 0,
    padding: variant === 'pills' ? theme.spacing.sm : 0,
    margin: `${theme.spacing.xl}px 0`,
    boxShadow: variant === 'pills' ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none',
    borderBottom: variant === 'underline' ? `2px solid ${theme.colors.border.light}` : 'none',
    position: 'relative' as const,
    flexWrap: 'wrap' as const,
    backdropFilter: variant === 'pills' ? 'blur(10px)' : 'none',
    border: variant === 'pills' ? `1px solid ${theme.colors.border.light}` : 'none',
  };

  const getTabStyle = (tab: Tab, isActive: boolean) => {
    const baseStyle = {
      backgroundColor: 'transparent',
      color: isActive ? theme.colors.text.primary : theme.colors.text.secondary,
      border: 'none',
      borderRadius: variant === 'pills' ? theme.borderRadius.lg : 0,
      padding: sizeStyles[size].padding,
      fontSize: sizeStyles[size].fontSize,
      fontWeight: isActive ? 700 : 500,
      cursor: tab.disabled ? 'not-allowed' : 'pointer',
      transition: `all ${theme.transitions.fast}`,
      position: 'relative' as const,
      display: 'flex',
      alignItems: 'center',
      gap: theme.spacing.xs,
      opacity: tab.disabled ? 0.5 : 1,
      fontFamily: theme.typography.fontFamily.sans,
      minWidth: '80px',
      justifyContent: 'center',
    };

    if (variant === 'pills' && isActive) {
      return {
        ...baseStyle,
        background: `linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.primaryLight})`,
        color: 'white',
        boxShadow: '0 4px 12px rgba(37, 99, 235, 0.3)',
        transform: 'translateY(-2px)',
        fontWeight: 700,
      };
    }

    if (variant === 'underline' && isActive) {
      return {
        ...baseStyle,
        borderBottom: `3px solid ${theme.colors.primary}`,
        color: theme.colors.primary,
        fontWeight: 700,
      };
    }

    return baseStyle;
  };

  const getHoverStyle = (tab: Tab, isActive: boolean) => {
    if (tab.disabled || isActive) return {};

    if (variant === 'pills') {
      return {
        background: `linear-gradient(135deg, ${theme.colors.background.tertiary}, ${theme.colors.background.secondary})`,
        transform: 'translateY(-2px)',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        color: theme.colors.primary,
      };
    }

    if (variant === 'underline') {
      return {
        borderBottom: `2px solid ${theme.colors.primary}`,
        color: theme.colors.primary,
      };
    }

    return {
      backgroundColor: theme.colors.background.tertiary,
    };
  };

  return (
    <div className={className} style={containerStyle}>
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        return (
          <button
            key={tab.id}
            onClick={() => !tab.disabled && onTabChange(tab.id)}
            style={getTabStyle(tab, isActive)}
            onMouseEnter={(e) => {
              const hoverStyle = getHoverStyle(tab, isActive);
              Object.assign(e.currentTarget.style, hoverStyle);
            }}
            onMouseLeave={(e) => {
              Object.assign(e.currentTarget.style, getTabStyle(tab, isActive));
            }}
            disabled={tab.disabled}
            role="tab"
            aria-selected={isActive}
            aria-disabled={tab.disabled}
          >
            {tab.icon && <span style={{ fontSize: '16px' }}>{tab.icon}</span>}
            {tab.label}
          </button>
        );
      })}
    </div>
  );
};
