import React from 'react';
import { theme } from '../../config/theme.config';
import type { WidgetPosition } from '../../types/dashboard.types';

interface WidgetContainerProps {
  children: React.ReactNode;
  title?: string;
  position: WidgetPosition;
  loading?: boolean;
  error?: Error | null;
}

export const WidgetContainer: React.FC<WidgetContainerProps> = ({
  children,
  title,
  position,
  loading = false,
  error = null,
}) => {
  const gridStyles = {
    gridColumn: `span ${position.w}`,
    gridRow: `span ${position.h}`,
  };

  return (
    <div
      style={{
        ...gridStyles,
        background: 'linear-gradient(135deg, #ffffff 0%, #fafbfc 100%)',
        borderRadius: theme.borderRadius.xl,
        boxShadow: theme.shadows.card,
        padding: theme.spacing.xl,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        transition: `all ${theme.transitions.normal}`,
        border: `1px solid ${theme.colors.border.light}`,
        minHeight: `${position.h * 120}px`,
        height: 'auto',
        position: 'relative',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.cardHover;
        e.currentTarget.style.transform = 'translateY(-6px)';
        e.currentTarget.style.borderColor = theme.colors.primary + '40';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.card;
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.borderColor = theme.colors.border.light;
      }}
    >
      {title && (
        <div style={{
          marginBottom: theme.spacing.lg,
          paddingBottom: theme.spacing.sm,
          borderBottom: `2px solid ${theme.colors.border.light}`,
        }}>
          <h3 style={{
            margin: 0,
            color: theme.colors.text.primary,
            fontSize: theme.typography.fontSize.lg,
            fontWeight: theme.typography.fontWeight.semibold,
            letterSpacing: '-0.3px',
            lineHeight: 1.3,
          }}>
            {title}
          </h3>
        </div>
      )}
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: loading || error ? 'center' : 'stretch',
        minHeight: 0,
        width: '100%',
      }}>
        {loading && (
          <div style={{ 
            color: theme.colors.text.secondary,
            fontSize: theme.typography.fontSize.sm,
          }}>
            Loading widget...
          </div>
        )}
        {error && (
          <div style={{ 
            color: theme.colors.error, 
            textAlign: 'center',
            fontSize: theme.typography.fontSize.sm,
            padding: theme.spacing.md,
          }}>
            Error: {error.message}
          </div>
        )}
        {!loading && !error && children}
      </div>
    </div>
  );
};