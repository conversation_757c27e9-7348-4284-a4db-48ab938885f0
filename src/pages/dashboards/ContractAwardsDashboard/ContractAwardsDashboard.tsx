import React, { useState, useEffect } from 'react';
import { PageLayout } from '../../../components/layout/PageLayout';
import { DashboardHeader } from '../../../components/layout/DashboardHeader';
import { DashboardActions } from '../../../components/layout/DashboardActions';
import { TabNavigation } from '../../../components/layout/TabNavigation';

import { LoadingSpinner } from '../../../components/layout/LoadingSpinner';
import { ErrorBoundary, ErrorDisplay } from '../../../components/layout/ErrorBoundary';
import { theme } from '../../../config/theme.config';
import { createWidget } from '../../../utils/widgetFactory';
import { contractAwardsDashboardConfig } from './contractAwardsDashboard.config';
import '../../../styles/dashboard.css';

export const ContractAwardsDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('MONTH');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { id: dashboardId, widgets, title, description } = contractAwardsDashboardConfig;

  // Simulate loading dashboard data
  useEffect(() => {
    const loadDashboard = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Simulate potential error (uncomment to test error state)
        // if (Math.random() > 0.8) {
        //   throw new Error('Failed to load dashboard data');
        // }

        setIsLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        setIsLoading(false);
      }
    };

    loadDashboard();
  }, []);

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Exporting Contract Awards Dashboard data...');
  };

  const handleRetry = () => {
    setError(null);
    setIsLoading(true);
    // Trigger reload
    window.location.reload();
  };

  // Define tabs for navigation
  const tabs = [
    { id: 'MONTH', label: 'Monthly', icon: '📅' },
    { id: 'YTD', label: 'Year to Date', icon: '📊' },
    { id: 'TTM', label: 'Trailing 12M', icon: '📈' },
    { id: 'ANNUAL', label: 'Annual', icon: '🗓️' },
  ];

  // Separate widgets by type
  const kpiWidgets = widgets.filter(widget => !widget.visibleOn);
  const tabSpecificWidgets = widgets.filter(widget => widget.visibleOn && widget.visibleOn.includes(activeTab));

  // Show loading state
  if (isLoading) {
    return (
      <PageLayout title="Contract Awards Dashboard" showHeader={false}>
        <div style={{
          minHeight: '100vh',
          background: theme.colors.backgroundGradient,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <LoadingSpinner
            size="lg"
            message="Loading Contract Awards Dashboard..."
          />
        </div>
      </PageLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <PageLayout title="Contract Awards Dashboard" showHeader={false}>
        <div style={{
          minHeight: '100vh',
          background: theme.colors.backgroundGradient,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: theme.spacing.xl,
        }}>
          <ErrorDisplay
            title="Dashboard Loading Error"
            message={error}
            onRetry={handleRetry}
            icon="📊"
          />
        </div>
      </PageLayout>
    );
  }

  return (
    <ErrorBoundary>
      <PageLayout title="Contract Awards Dashboard" showHeader={false}>
        <div style={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%)',
          padding: `${theme.spacing.lg}px ${theme.spacing.xl}px`,
        }}>
          {/* Dashboard Header */}
          <div style={{
            maxWidth: '1400px',
            margin: '0 auto',
            marginBottom: theme.spacing.xl,
          }}>
            <DashboardHeader
              title={title}
              description={description}
              actions={<DashboardActions onExport={handleExport} />}
            />
          </div>

          {/* KPI Section - Always visible widgets */}
          <div style={{
            maxWidth: '1400px',
            margin: '0 auto',
            marginBottom: theme.spacing.xxl,
          }}>
            <div
              className="responsive-grid"
              style={{
                display: 'grid',
                gridTemplateColumns: `repeat(auto-fit, minmax(300px, 1fr))`,
                gap: theme.spacing.xl,
                marginBottom: theme.spacing.lg,
              }}
            >
              {kpiWidgets.map((widget, index) => (
                <div
                  key={widget.id}
                  className={`slide-in-up stagger-${index + 1} hover-lift`}
                  style={{ animationFillMode: 'both' }}
                >
                  {createWidget(widget, dashboardId, activeTab)}
                </div>
              ))}
            </div>
          </div>

          {/* Tab Navigation */}
          <div style={{
            maxWidth: '1400px',
            margin: '0 auto',
            marginBottom: theme.spacing.xl,
          }}>
            <TabNavigation
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={handleTabChange}
              variant="pills"
              size="md"
            />
          </div>

          {/* Tab-specific widgets */}
          <div style={{
            maxWidth: '1400px',
            margin: '0 auto',
          }}>
            <div
              className="responsive-grid"
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(12, 1fr)',
                gap: theme.spacing.xl,
                gridAutoRows: 'minmax(350px, auto)',
              }}
            >
              {tabSpecificWidgets.map((widget, index) => (
                <div
                  key={widget.id}
                  className={`slide-in-up stagger-${(index % 6) + 1} hover-lift`}
                  style={{ animationFillMode: 'both' }}
                >
                  {createWidget(widget, dashboardId, activeTab)}
                </div>
              ))}
            </div>
          </div>
        </div>
      </PageLayout>
    </ErrorBoundary>
  );
};